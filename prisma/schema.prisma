// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model Post {
  id        String   @id @default(cuid())
  title     String
  content   String?
  published Boolean  @default(false)
  authorId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("posts")
}

model ExcelFile {
  id               String   @id @default(cuid())
  fileName         String
  fileSize         Int
  rowCount         Int?     @default(0)
  uploadDate       DateTime @default(now())
  originalStructure Json?   // JSON for PostgreSQL
  columnMapping    Json?    // Applied column mapping for this file
  detectedHeaders  Json?    // Headers detected in this file
  rows             ExcelRow[]
  aggregated       AggregatedItem[]

  @@index([uploadDate])
  @@map("excel_files")
}

model ExcelRow {
  id               String    @id @default(cuid())
  itemId           String?
  name             String
  quantity         Float
  unit             String
  originalRowIndex Int?      // Position in original Excel file
  fileId           String
  file             ExcelFile @relation(fields: [fileId], references: [id], onDelete: Cascade)
  createdAt        DateTime  @default(now())

  @@index([itemId, name, unit])
  @@index([fileId])
  @@index([name]) // For name-based searches
  @@index([createdAt]) // For time-based queries
  @@index([fileId, originalRowIndex]) // For file-specific ordering
  @@map("excel_rows")
}

model AggregatedItem {
  id          String     @id @default(cuid())
  itemId      String?
  name        String
  quantity    Float
  unit        String
  fileId      String?
  file        ExcelFile? @relation(fields: [fileId], references: [id], onDelete: Cascade)
  sourceFiles Json?      // JSON array of file IDs
  count       Int?       @default(1)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  @@unique([itemId, name, unit], name: "itemId_name_unit")
  @@index([itemId, name, unit])
  @@index([fileId])
  @@index([name]) // For name-based searches
  @@index([quantity]) // For quantity-based sorting
  @@index([updatedAt]) // For recently updated items
  @@index([count]) // For count-based queries
  @@map("aggregated_items")
}

model ColumnMapping {
  id          String   @id @default(cuid())
  name        String   // User-friendly name for this mapping
  description String?  // Optional description
  isDefault   Boolean  @default(false)
  mapping     Json     // Column mapping configuration as JSON
  headers     Json?    // Sample headers this mapping was created for
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Track usage
  usageCount  Int      @default(0)
  lastUsed    DateTime?
  
  @@index([isDefault])
  @@index([usageCount])
  @@index([lastUsed])
  @@map("column_mappings")
}