import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import ComparisonPage from '../page'

// Mock the toast hook
jest.mock('@/hooks/use-toast', () => ({
  toast: jest.fn(),
}))

// Mock Next.js Link
jest.mock('next/link', () => {
  return function MockLink({ children, href }: { children: React.ReactNode; href: string }) {
    return React.createElement('a', { href }, children)
  }
})

// Mock fetch
const mockFetch = jest.fn()
global.fetch = mockFetch

describe('Comparison Page', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFetch.mockClear()
  })

  it('renders the comparison page title', () => {
    render(<ComparisonPage />)
    expect(screen.getAllByText('Porównanie Miesięczne Inwentarza')).toHaveLength(2) // Title appears in multiple places
    expect(screen.getAllByText(/Porównaj bieżący inwentarz z poprzednim miesiącem/)).toHaveLength(2) // Description also appears in multiple places
  })

  it('renders back navigation link', () => {
    render(<ComparisonPage />)
    const backLink = screen.getByText('Powrót do zarządzania inwentarzem')
    expect(backLink.closest('a')).toHaveAttribute('href', '/')
  })

  it('shows current month data selection options', () => {
    render(<ComparisonPage />)
    expect(screen.getByText('Dane Bieżące (Obecny miesiąc)')).toBeInTheDocument()
    expect(screen.getByText('Użyj bieżącej agregacji')).toBeInTheDocument()
    expect(screen.getByText('Prześlij nowy plik')).toBeInTheDocument()
  })

  it('shows previous month file upload section', () => {
    render(<ComparisonPage />)
    expect(screen.getByText('Plik Poprzedniego Miesiąca')).toBeInTheDocument()
    expect(screen.getByText('Prześlij plik Excel z poprzedniego miesiąca')).toBeInTheDocument()
  })

  it('loads initial data on mount', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        aggregated: [
          { id: '1', itemId: 'A001', name: 'Product A', quantity: 10, unit: 'kg' },
          { id: '2', itemId: 'A002', name: 'Product B', quantity: 5, unit: 'l' },
        ],
      }),
    })

    render(<ComparisonPage />)

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/excel/data?includeRaw=true')
    })

    // Check if the data count is displayed
    await waitFor(() => {
      expect(screen.getByText(/2 pozycji/)).toBeInTheDocument()
    })
  })

  it('shows debug information correctly', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        aggregated: [
          { id: '1', itemId: 'A001', name: 'Product A', quantity: 10, unit: 'kg' },
        ],
      }),
    })

    render(<ComparisonPage />)

    await waitFor(() => {
      expect(screen.getByText(/Bieżące pozycje: 1/)).toBeInTheDocument()
      expect(screen.getByText(/Poprzedni miesiąc: 0/)).toBeInTheDocument()
      expect(screen.getByText(/Różnice: 0/)).toBeInTheDocument()
    })
  })

  it('handles file selection for previous month', async () => {
    const user = userEvent.setup()
    render(<ComparisonPage />)

    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    expect(fileInput).toBeInTheDocument()
    
    const file = new File(['excel content'], 'previous-month.xlsx', {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })

    await user.upload(fileInput, file)

    await waitFor(() => {
      expect(screen.getByText('Wybrano: previous-month.xlsx')).toBeInTheDocument()
    })
  })

  it('disables comparison button when no previous month file', () => {
    render(<ComparisonPage />)
    const compareButton = screen.getByText('Porównaj Inwentarze Miesięczne')
    expect(compareButton).toBeDisabled()
  })

  it('handles successful comparison', async () => {
    const user = userEvent.setup()
    const { toast } = jest.requireMock('@/hooks/use-toast')

    // Mock initial data
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        aggregated: [
          { id: '1', itemId: 'A001', name: 'Product A', quantity: 15, unit: 'kg' },
        ],
      }),
    })

    // Mock previous month file upload
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        aggregated: [
          { id: '1', itemId: 'A001', name: 'Product A', quantity: 10, unit: 'kg' },
        ],
      }),
    })

    render(<ComparisonPage />)

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText(/1 pozycji/)).toBeInTheDocument()
    })

    // Add a previous month file
    const fileInput = document.querySelector('input[type="file"]')
    if (fileInput) {
      const file = new File(['excel content'], 'previous-month.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })

      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      })

      fireEvent.change(fileInput)

      await waitFor(() => {
        expect(screen.getByText('Wybrano: previous-month.xlsx')).toBeInTheDocument()
      })

      // Click compare button
      const compareButton = screen.getByText('Porównaj Inwentarze Miesięczne')
      await user.click(compareButton)

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/excel/upload', {
          method: 'POST',
          body: expect.any(FormData),
        })
      })

      await waitFor(() => {
        expect(toast).toHaveBeenCalledWith({
          title: 'Porównanie miesięczne zakończone',
          description: expect.stringContaining('Porównano'),
        })
      })
    }
  })

  it('shows error when trying to compare without previous month file', async () => {
    const user = userEvent.setup()
    const { toast } = jest.requireMock('@/hooks/use-toast')

    render(<ComparisonPage />)

    const compareButton = screen.getByText('Porównaj Inwentarze Miesięczne')
    
    // Force enable the button for testing
    Object.defineProperty(compareButton, 'disabled', {
      value: false,
      writable: true,
    })

    await user.click(compareButton)

    await waitFor(() => {
      expect(toast).toHaveBeenCalledWith({
        title: 'Błąd',
        description: 'Prześlij plik poprzedniego miesiąca',
        variant: 'destructive',
      })
    })
  })

  it('toggles between current aggregation and new file options', async () => {
    const user = userEvent.setup()
    render(<ComparisonPage />)

    // Initially "Użyj bieżącej agregacji" should be selected
    const useCurrentButton = screen.getByText('Użyj bieżącej agregacji')
    const uploadNewButton = screen.getByText('Prześlij nowy plik')

    expect(useCurrentButton.closest('button')).toHaveClass('bg-primary')
    
    // Click on "Prześlij nowy plik"
    await user.click(uploadNewButton)

    await waitFor(() => {
      expect(uploadNewButton.closest('button')).toHaveClass('bg-primary')
    })
  })

  it('renders comparison results when data is available', async () => {
    // Mock data that will trigger comparison results
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        aggregated: [
          { id: '1', itemId: 'A001', name: 'Product A', quantity: 15, unit: 'kg' },
        ],
      }),
    })

    const { rerender } = render(<ComparisonPage />)

    // Manually trigger state that would show results
    // This is a simplified test - in real usage, this would happen after comparison
    
    // Since state management is internal, we'll verify the basic structure exists
    expect(screen.getByText('Wybierz dane do porównania')).toBeInTheDocument()
  })

  it('shows correct statistics when comparison data is available', () => {
    render(<ComparisonPage />)
    
    // The component should render properly without comparison data
    expect(screen.getByText('Porównanie Miesięczne Inwentarza')).toBeInTheDocument()
  })

  it('handles API errors gracefully', async () => {
    const { toast } = jest.requireMock('@/hooks/use-toast')
    
    // Mock API error
    mockFetch.mockRejectedValueOnce(new Error('API Error'))

    render(<ComparisonPage />)

    // The component should still render, and errors should be handled
    await waitFor(() => {
      expect(screen.getByText('Porównanie Miesięczne Inwentarza')).toBeInTheDocument()
    })
  })
})