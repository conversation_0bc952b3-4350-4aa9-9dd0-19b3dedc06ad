/**
 * @jest-environment node
 */

import { POST } from '../upload/route'
import { NextRequest } from 'next/server'

// Mock Prisma
jest.mock('@/lib/db', () => ({
  __esModule: true,
  db: {
    excelFile: {
      create: jest.fn(),
    },
    excelRow: {
      createMany: jest.fn(),
    },
    aggregatedItem: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      createMany: jest.fn(),
    },
  },
}))

// Mock XLSX
jest.mock('xlsx', () => ({
  read: jest.fn(),
  utils: {
    sheet_to_json: jest.fn(),
  },
}))

import { db } from '@/lib/db'
import * as XLSX from 'xlsx'

const mockPrisma = db as jest.Mocked<typeof db>
const mockXLSX = XLSX as jest.Mocked<typeof XLSX>

describe('/api/excel/upload', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should handle file upload successfully', async () => {
    // Mock Excel file content - should be arrays since upload route uses { header: 1 }
    const mockExcelData = [
      ['L.p.', 'Nr indeksu', 'Nazwa towaru', 'Ilosc', 'Jednostka'], // Header
      [1, 'A001', 'Product A', 10, 'kg'],
      [2, 'A002', 'Product B', 5, 'l'],
    ]

    // Mock XLSX functions
    mockXLSX.read.mockReturnValue({
      SheetNames: ['Sheet1'],
      Sheets: {
        Sheet1: {},
      },
    } as any)
    
    mockXLSX.utils.sheet_to_json.mockReturnValue(mockExcelData)

    // Mock Prisma responses
    mockPrisma.excelFile.create.mockResolvedValue({
      id: 'file-123',
      name: 'test.xlsx',
      size: 1024,
      uploadDate: new Date(),
    } as any)

    mockPrisma.excelRow.createMany.mockResolvedValue({ count: 2 })
    mockPrisma.aggregatedItem.findUnique.mockResolvedValue(null) // No existing items
    mockPrisma.aggregatedItem.create.mockResolvedValue({ id: 'item-123' } as any)
    mockPrisma.aggregatedItem.createMany.mockResolvedValue({ count: 2 })

    // Create mock request with file
    const formData = new FormData()
    // Create a file with minimum size requirement (>100 bytes)
    const fileContent = 'x'.repeat(200) // 200 bytes to exceed minimum
    const file = new File([fileContent], 'test.xlsx', {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    formData.append('file', file)

    const request = new NextRequest('http://localhost:3000/api/excel/upload', {
      method: 'POST',
      body: formData,
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data).toHaveProperty('fileId')
    expect(data).toHaveProperty('rows')
    expect(data).toHaveProperty('aggregated')
    expect(mockPrisma.excelFile.create).toHaveBeenCalled()
    expect(mockPrisma.excelRow.createMany).toHaveBeenCalled()
    expect(mockPrisma.aggregatedItem.create).toHaveBeenCalled()
  })

  it('should return error when no file is provided', async () => {
    const formData = new FormData()
    const request = new NextRequest('http://localhost:3000/api/excel/upload', {
      method: 'POST',
      body: formData,
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('No file uploaded')
  })

  it('should return error for invalid file type', async () => {
    const formData = new FormData()
    const file = new File(['content'], 'test.txt', { type: 'text/plain' })
    formData.append('file', file)

    const request = new NextRequest('http://localhost:3000/api/excel/upload', {
      method: 'POST',
      body: formData,
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Invalid file type. Please upload an Excel file (.xlsx or .xls)')
  })

  it('should handle Excel parsing errors', async () => {
    mockXLSX.read.mockImplementation(() => {
      throw new Error('Invalid Excel file')
    })

    const formData = new FormData()
    const fileContent = 'x'.repeat(200) // 200 bytes to exceed minimum
    const file = new File([fileContent], 'test.xlsx', {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    formData.append('file', file)

    const request = new NextRequest('http://localhost:3000/api/excel/upload', {
      method: 'POST',
      body: formData,
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data.error).toBe('Failed to process Excel file')
  })

  it('should aggregate items correctly', async () => {
    const mockExcelData = [
      ['L.p.', 'Nr indeksu', 'Nazwa towaru', 'Ilosc', 'Jednostka'], // Header
      [1, 'A001', 'Product A', 10, 'kg'],
      [2, 'A001', 'Product A', 5, 'kg'],
      [3, 'A002', 'Product B', 3, 'l'],
    ]

    mockXLSX.read.mockReturnValue({
      SheetNames: ['Sheet1'],
      Sheets: { Sheet1: {} },
    } as any)
    
    mockXLSX.utils.sheet_to_json.mockReturnValue(mockExcelData)

    mockPrisma.excelFile.create.mockResolvedValue({
      id: 'file-123',
      name: 'test.xlsx',
      size: 1024,
      uploadDate: new Date(),
    } as any)

    mockPrisma.excelRow.createMany.mockResolvedValue({ count: 3 })
    mockPrisma.aggregatedItem.findUnique.mockResolvedValue(null) // No existing items
    mockPrisma.aggregatedItem.create.mockResolvedValue({ id: 'item-123' } as any)
    mockPrisma.aggregatedItem.createMany.mockResolvedValue({ count: 2 })

    const formData = new FormData()
    const fileContent = 'x'.repeat(200) // 200 bytes to exceed minimum
    const file = new File([fileContent], 'test.xlsx', {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    formData.append('file', file)

    const request = new NextRequest('http://localhost:3000/api/excel/upload', {
      method: 'POST',
      body: formData,
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data).toHaveProperty('fileId')
    expect(data).toHaveProperty('rows')
    expect(data).toHaveProperty('aggregated')
    
    // Check that aggregated data is returned in response
    expect(data.aggregated).toHaveLength(2) // Should have 2 unique items
    expect(data.rows).toHaveLength(3) // Should have 3 raw rows
    
    // Find aggregated Product A
    const productA = data.aggregated.find(item => item.itemId === 'A001')
    expect(productA).toBeDefined()
    expect(productA.quantity).toBe(15) // 10 + 5
  })
})